import React, { useMemo, useEffect, useRef, useCallback } from "react";
import * as THREE from "three";
import { useGLTF } from "@react-three/drei";
import { useFrame } from "@react-three/fiber";

interface Props {
  fabricCanvas?: HTMLCanvasElement;
  updateTrigger?: number;
}

const FabricTexturedBox: React.FC<Props> = ({ fabricCanvas, updateTrigger }) => {
  const textureRef = useRef<THREE.CanvasTexture | null>(null);
  const { scene } = useGLTF('/assets/tshirt.glb');
  const clonedSceneRef = useRef<THREE.Group | null>(null);
  const materialsRef = useRef<THREE.MeshStandardMaterial[]>([]);

  // Initialize texture when canvas appears
  const texture = useMemo(() => {
    if (!fabricCanvas) return null;
    const tex = new THREE.CanvasTexture(fabricCanvas);
    tex.needsUpdate = true;
    tex.flipY = false; // Important for GLB models
    tex.wrapS = THREE.ClampToEdgeWrapping;
    tex.wrapT = THREE.ClampToEdgeWrapping;
    tex.generateMipmaps = false;
    tex.minFilter = THREE.LinearFilter;
    tex.magFilter = THREE.LinearFilter;
    textureRef.current = tex;
    return tex;
  }, [fabricCanvas]);

  // Update texture function
  const updateTexture = useCallback(() => {
    if (textureRef.current && fabricCanvas) {
      textureRef.current.needsUpdate = true;

      // Force update all materials using this texture
      materialsRef.current.forEach((material) => {
        material.needsUpdate = true;
      });
    }
  }, [fabricCanvas]);

  // Force update when updateTrigger changes
  useEffect(() => {
    updateTexture();
  }, [updateTrigger, updateTexture]);

  // Use frame to continuously update texture (for real-time updates)
  useFrame(() => {
    if (textureRef.current && fabricCanvas) {
      textureRef.current.needsUpdate = true;
    }
  });

  // Clone scene and apply texture to the t-shirt model
  useEffect(() => {
    if (scene && texture) {
      // Clone the scene to avoid modifying the original
      const clonedScene = scene.clone();
      clonedSceneRef.current = clonedScene;

      // Clear previous materials
      materialsRef.current = [];

      clonedScene.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          // Clone materials to avoid modifying the original
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material = child.material.map((mat) => {
                if (mat instanceof THREE.MeshStandardMaterial) {
                  const clonedMat = mat.clone();
                  clonedMat.map = texture;
                  clonedMat.needsUpdate = true;
                  materialsRef.current.push(clonedMat);
                  return clonedMat;
                }
                return mat;
              });
            } else if (child.material instanceof THREE.MeshStandardMaterial) {
              const clonedMat = child.material.clone();
              clonedMat.map = texture;
              clonedMat.needsUpdate = true;
              child.material = clonedMat;
              materialsRef.current.push(clonedMat);
            }
          }
        }
      });
    }
  }, [texture, scene]);

  return clonedSceneRef.current ? (
    <primitive
      object={clonedSceneRef.current}
      scale={[1, 1, 1]}
      position={[0, 0, 0]}
      rotation={[0, 0, 0]}
    />
  ) : null;
};

// Preload the GLB file
useGLTF.preload('/assets/tshirt.glb');

export default FabricTexturedBox;
