import React, { useMemo, useEffect, useRef, useCallback } from "react";
import * as THREE from "three";
import { useGLTF } from "@react-three/drei";
import { useFrame } from "@react-three/fiber";

interface Props {
  fabricCanvas?: HTMLCanvasElement;
  updateTrigger?: number;
}

const FabricTexturedBox: React.FC<Props> = ({ fabricCanvas, updateTrigger }) => {
  const textureRef = useRef<THREE.CanvasTexture | null>(null);
  const { scene } = useGLTF('/assets/tshirt.glb');
  const clonedSceneRef = useRef<THREE.Group | null>(null);
  const materialsRef = useRef<THREE.Material[]>([]);

  // Initialize texture when canvas appears
  const texture = useMemo(() => {
    if (!fabricCanvas) {
      console.log('No fabric canvas available');
      return null;
    }
    console.log('Creating texture from canvas:', fabricCanvas.width, 'x', fabricCanvas.height);
    const tex = new THREE.CanvasTexture(fabricCanvas);
    tex.needsUpdate = true;
    tex.flipY = true; // Try with flipY true for better compatibility
    tex.wrapS = THREE.RepeatWrapping;
    tex.wrapT = THREE.RepeatWrapping;
    tex.generateMipmaps = true;
    tex.minFilter = THREE.LinearMipmapLinearFilter;
    tex.magFilter = THREE.LinearFilter;
    textureRef.current = tex;
    console.log('Texture created:', tex);
    return tex;
  }, [fabricCanvas]);

  // Update texture function
  const updateTexture = useCallback(() => {
    if (textureRef.current && fabricCanvas) {
      textureRef.current.needsUpdate = true;

      // Force update all materials using this texture
      materialsRef.current.forEach((material) => {
        material.needsUpdate = true;
      });
    }
  }, [fabricCanvas]);

  // Force update when updateTrigger changes
  useEffect(() => {
    updateTexture();
  }, [updateTrigger, updateTexture]);

  // Use frame to continuously update texture (for real-time updates)
  useFrame(() => {
    if (textureRef.current && fabricCanvas) {
      textureRef.current.needsUpdate = true;
    }
  });

  // Clone scene and apply texture to the t-shirt model
  useEffect(() => {
    if (scene && texture) {
      console.log('Applying texture to scene');
      // Clone the scene to avoid modifying the original
      const clonedScene = scene.clone();
      clonedSceneRef.current = clonedScene;

      // Clear previous materials
      materialsRef.current = [];

      clonedScene.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          console.log('Found mesh:', child.name, 'Material:', child.material);

          // Clone materials to avoid modifying the original
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material = child.material.map((mat) => {
                const clonedMat = mat.clone();
                // Apply texture to any material type
                if (clonedMat instanceof THREE.MeshStandardMaterial ||
                    clonedMat instanceof THREE.MeshBasicMaterial ||
                    clonedMat instanceof THREE.MeshPhongMaterial ||
                    clonedMat instanceof THREE.MeshLambertMaterial) {
                  clonedMat.map = texture;
                  clonedMat.needsUpdate = true;
                  materialsRef.current.push(clonedMat);
                }
                return clonedMat;
              });
            } else {
              const clonedMat = child.material.clone();
              // Apply texture to any material type
              if (clonedMat instanceof THREE.MeshStandardMaterial ||
                  clonedMat instanceof THREE.MeshBasicMaterial ||
                  clonedMat instanceof THREE.MeshPhongMaterial ||
                  clonedMat instanceof THREE.MeshLambertMaterial) {
                clonedMat.map = texture;
                clonedMat.needsUpdate = true;
                child.material = clonedMat;
                materialsRef.current.push(clonedMat);
              }
            }
          }
        }
      });
    }
  }, [texture, scene]);

  return (
    <group>
      {clonedSceneRef.current ? (
        <primitive
          object={clonedSceneRef.current}
          scale={[2, 2, 2]}
          position={[0, -1, 0]}
          rotation={[0, Math.PI, 0]}
        />
      ) : (
        // Fallback: simple textured plane if GLB doesn't load
        texture && (
          <mesh position={[0, 0, 0]} rotation={[-Math.PI / 2, 0, 0]}>
            <planeGeometry args={[2, 2]} />
            <meshStandardMaterial map={texture} transparent />
          </mesh>
        )
      )}
    </group>
  );
};

// Preload the GLB file
useGLTF.preload('/assets/tshirt.glb');

export default FabricTexturedBox;
