import React, { useMemo, useEffect, useRef } from "react";
import * as THREE from "three";
import { useGLTF } from "@react-three/drei";

interface Props {
  fabricCanvas?: HTMLCanvasElement;
  updateTrigger?: number;
}

const FabricTexturedBox: React.FC<Props> = ({ fabricCanvas, updateTrigger }) => {
  const textureRef = useRef<THREE.CanvasTexture | null>(null);
  const { scene } = useGLTF('/assets/tshirt.glb');

  // Initialize texture when canvas appears
  const texture = useMemo(() => {
    if (!fabricCanvas) return null;
    const tex = new THREE.CanvasTexture(fabricCanvas);
    tex.needsUpdate = true;
    tex.flipY = false; // Important for GLB models
    textureRef.current = tex;
    return tex;
  }, [fabricCanvas]);

  // Force update when fabricCanvas changes or updateTrigger changes
  useEffect(() => {
    if (textureRef.current) {
      textureRef.current.needsUpdate = true;
    }
  }, [fabricCanvas, updateTrigger]);

  // Apply texture to the t-shirt model
  useEffect(() => {
    if (texture && scene) {
      scene.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          // Apply the fabric texture to the mesh material
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((mat) => {
                if (mat instanceof THREE.MeshStandardMaterial) {
                  mat.map = texture;
                  mat.needsUpdate = true;
                }
              });
            } else if (child.material instanceof THREE.MeshStandardMaterial) {
              child.material.map = texture;
              child.material.needsUpdate = true;
            }
          }
        }
      });
    }
  }, [texture, scene]);

  return (
    <primitive
      object={scene}
      scale={[1, 1, 1]}
      position={[0, 0, 0]}
      rotation={[0, 0, 0]}
    />
  );
};

// Preload the GLB file
useGLTF.preload('/assets/tshirt.glb');

export default FabricTexturedBox;
