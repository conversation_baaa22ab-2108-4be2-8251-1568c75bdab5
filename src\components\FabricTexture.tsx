import React, { useMemo, useEffect, useRef } from "react";
import * as THREE from "three";

interface Props {
  fabricCanvas?: HTMLCanvasElement;
}

const FabricTexturedBox: React.FC<Props> = ({ fabricCanvas }) => {
  const textureRef = useRef<THREE.CanvasTexture | null>(null);

  // Initialize texture when canvas appears
  const texture = useMemo(() => {
    if (!fabricCanvas) return null;
    const tex = new THREE.CanvasTexture(fabricCanvas);
    tex.needsUpdate = true;
    textureRef.current = tex;
    return tex;
  }, [fabricCanvas]);

  // Force update when fabricCanvas changes
  useEffect(() => {
    if (textureRef.current) {
      textureRef.current.needsUpdate = true;
    }
  }, [fabricCanvas]);

  return (
    <mesh rotation={[0.5, 0.5, 0]}>
      <boxGeometry args={[2, 2, 2]} />
      <meshStandardMaterial map={texture ?? undefined} />
    </mesh>
  );
};

export default FabricTexturedBox;
