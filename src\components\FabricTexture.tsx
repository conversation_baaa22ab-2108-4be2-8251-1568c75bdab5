import React, { useMemo, useEffect, useRef } from "react";
import * as THREE from "three";
import { useGLTF } from "@react-three/drei";

interface Props {
  fabricCanvas?: HTMLCanvasElement;
}

const FabricTexturedShirt: React.FC<Props> = ({ fabricCanvas }) => {
  const { scene } = useGLTF("/assets/tshirt.glb");
  const clonedScene = useMemo(() => scene.clone(), [scene]);
  const textureRef = useRef<THREE.CanvasTexture | null>(null);

  useEffect(() => {
    if (fabricCanvas) {
      textureRef.current = new THREE.CanvasTexture(fabricCanvas);
    }
  }, [fabricCanvas]);

  useEffect(() => {
    if (textureRef.current) {
      textureRef.current.needsUpdate = true;
    }
  });

  useEffect(() => {
    if (!textureRef.current) return;

    clonedScene.traverse((child) => {
      if (child instanceof THREE.Mesh) {
        child.material.map = textureRef.current;
        child.material.needsUpdate = true;
      }
    });
  }, [clonedScene]);

  return <primitive object={clonedScene} />;
};

export default FabricTexturedShirt;
