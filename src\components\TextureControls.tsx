import React from 'react';

interface TextureControlsProps {
  modelType: 'box' | 'sphere' | 'plane' | 'cylinder' | 'tshirt';
  onModelTypeChange: (type: 'box' | 'sphere' | 'plane' | 'cylinder' | 'tshirt') => void;
  textureRepeat: { x: number; y: number };
  onTextureRepeatChange: (repeat: { x: number; y: number }) => void;
  textureOffset: { x: number; y: number };
  onTextureOffsetChange: (offset: { x: number; y: number }) => void;
}

const TextureControls: React.FC<TextureControlsProps> = ({
  modelType,
  onModelTypeChange,
  textureRepeat,
  onTextureRepeatChange,
  textureOffset,
  onTextureOffsetChange,
}) => {
  return (
    <div className="p-4 bg-gray-100 rounded-lg space-y-4">
      <h3 className="text-lg font-semibold">3D Model & Texture Controls</h3>
      
      {/* Model Type Selection */}
      <div>
        <label className="block text-sm font-medium mb-2">Model Type:</label>
        <select
         aria-label="Texture repeat Y"
          value={modelType}
          onChange={(e) => onModelTypeChange(e.target.value as any)}
          className="w-full p-2 border border-gray-300 rounded"
        >
          <option value="tshirt">T-Shirt (GLB)</option>
          <option value="box">Box</option>
          <option value="sphere">Sphere</option>
          <option value="plane">Plane</option>
          <option value="cylinder">Cylinder</option>
        </select>
      </div>

      {/* Texture Repeat Controls */}
      <div>
        <label className="block text-sm font-medium mb-2">Texture Repeat:</label>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-xs text-gray-600">X:</label>
            <input
             aria-label="Texture repeat x"
              type="number"
              min="0.1"
              max="10"
              step="0.1"
              value={textureRepeat.x}
              onChange={(e) => onTextureRepeatChange({
                ...textureRepeat,
                x: parseFloat(e.target.value) || 1
              })}
              className="w-full p-1 border border-gray-300 rounded text-sm"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-600">Y:</label>
            <input
            aria-label="Texture repeat Y"
              type="number"
              min="0.1"
              max="10"
              step="0.1"
              value={textureRepeat.y}
              onChange={(e) => onTextureRepeatChange({
                ...textureRepeat,
                y: parseFloat(e.target.value) || 1
              })}
              className="w-full p-1 border border-gray-300 rounded text-sm"
            />
          </div>
        </div>
      </div>

      {/* Texture Offset Controls */}
      <div>
        <label className="block text-sm font-medium mb-2">Texture Offset:</label>
        <div className="grid grid-cols-2 gap-2">
          <div>
            <label className="block text-xs text-gray-600">X:</label>
            <input
              aria-label="Texture Offset x"
              type="number"
              min="-1"
              max="1"
              step="0.1"
              value={textureOffset.x}
              onChange={(e) => onTextureOffsetChange({
                ...textureOffset,
                x: parseFloat(e.target.value) || 0
              })}
              className="w-full p-1 border border-gray-300 rounded text-sm"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-600">Y:</label>
            <input
              aria-label="Texture Offset Y"
              type="number"
              min="-1"
              max="1"
              step="0.1"
              value={textureOffset.y}
              onChange={(e) => onTextureOffsetChange({
                ...textureOffset,
                y: parseFloat(e.target.value) || 0
              })}
              className="w-full p-1 border border-gray-300 rounded text-sm"
            />
          </div>
        </div>
      </div>

      {/* Reset Button */}
      <button
        type="button"
        onClick={() => {
          onTextureRepeatChange({ x: 1, y: 1 });
          onTextureOffsetChange({ x: 0, y: 0 });
        }}
        className="w-full p-2 bg-gray-500 text-white rounded hover:bg-gray-600"
      >
        Reset Texture Settings
      </button>
    </div>
  );
};

export default TextureControls;
