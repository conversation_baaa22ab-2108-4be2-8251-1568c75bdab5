// src/utils/canvasTools.ts
import * as fabric  from "fabric";

export function addText(canvas: fabric.Canvas) {
  const text = new fabric.Textbox("New Text", {
    left: 100,
    top: 100,
    fontSize: 20,
    fill: "#000",
  });
  canvas.add(text);
  canvas.setActiveObject(text);
  canvas.renderAll();
}

// Add Rectangle
export function addRectangle(canvas: fabric.Canvas) {
  const rect = new fabric.Rect({
    left: 100,
    top: 100,
    fill: "red",
    width: 100,
    height: 100,
  });
  canvas.add(rect);
  canvas.setActiveObject(rect);
  canvas.renderAll();
}

// Add Circle
export function addCircle(canvas: fabric.Canvas) {
  const circle = new fabric.Circle({
    left: 150,
    top: 150,
    radius: 50,
    fill: "blue",
  });
  canvas.add(circle);
  canvas.setActiveObject(circle);
  canvas.renderAll();
}

// Add Line
export function addLine(canvas: fabric.Canvas) {
  const line = new fabric.Line([50, 100, 200, 100], {
    stroke: "black",
    strokeWidth: 2,
  });
  canvas.add(line);
  canvas.setActiveObject(line);
  canvas.renderAll();
}
