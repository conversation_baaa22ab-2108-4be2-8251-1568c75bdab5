{"name": "uv-texture-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-three/drei": "^10.7.3", "@react-three/fiber": "^9.3.0", "@tailwindcss/vite": "^4.1.12", "fabric": "^6.7.1", "framer-motion": "^12.23.12", "lucide-react": "^0.540.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.1", "tailwindcss": "^4.1.12", "three": "^0.179.1", "three-stdlib": "^2.36.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@types/three": "^0.179.0", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}