// src/pages/ThreeScene.tsx
import React, { useState } from "react";
import { Canvas } from "@react-three/fiber";
import { OrbitControls, Environment } from "@react-three/drei";
import FabricCanvas from "./FabricCanvas";
import FabricTexturedShirt from "../components/FabricTexture";

const ThreeScene: React.FC = () => {
  const [fabricCanvas, setFabricCanvas] = useState<HTMLCanvasElement>();
  const [updateTrigger, setUpdateTrigger] = useState(0);

  const handleCanvasUpdate = () => {
    setUpdateTrigger(prev => prev + 1);
  };

  return (
    <div className="flex">
      {/* Fabric.js editor */}
      <div className="w-1/2">
        <FabricCanvas onReady={setFabricCanvas} onUpdate={handleCanvasUpdate} />
      </div>

      {/* 3D viewer */}
      <div className="w-1/2 h-screen bg-gray-900">
        <Canvas camera={{ position: [0, 0, 4], fov: 50 }}>
          <ambientLight intensity={0.6} />
          <directionalLight position={[5, 5, 5]} intensity={1} />
          <directionalLight position={[-5, -5, -5]} intensity={0.5} />
          <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
          <Environment preset="studio" />

          {fabricCanvas && <FabricTexturedShirt fabricCanvas={fabricCanvas} updateTrigger={updateTrigger} />}
        </Canvas>
      </div>
    </div>
  );
};

export default ThreeScene;
