// src/pages/ThreeScene.tsx
import React, { useState } from "react";
import { Canvas } from "@react-three/fiber";
import { OrbitControls, Environment } from "@react-three/drei";
import FabricCanvas from "./FabricCanvas";
import FabricTexturedShirt from "../components/FabricTexture";

const ThreeScene: React.FC = () => {
  const [fabricCanvas, setFabricCanvas] = useState<HTMLCanvasElement>();

  return (
    <div className="flex">
      {/* Fabric.js editor */}
      <div className="w-1/2">
        <FabricCanvas onReady={setFabricCanvas} />
      </div>

      {/* 3D viewer */}
      <div className="w-1/2 h-screen bg-gray-900">
        <Canvas camera={{ position: [0, 1.5, 3], fov: 50 }}>
          <ambientLight intensity={0.5} />
          <directionalLight position={[2, 2, 2]} intensity={1} />
          <OrbitControls />
          <Environment preset="studio" />

          {fabricCanvas && <FabricTexturedShirt fabricCanvas={fabricCanvas} />}
        </Canvas>
      </div>
    </div>
  );
};

export default ThreeScene;
