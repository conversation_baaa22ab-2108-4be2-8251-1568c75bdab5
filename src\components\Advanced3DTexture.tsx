import React, { useMemo, useEffect, useRef } from "react";
import * as THREE from "three";
import { useGLTF } from "@react-three/drei";

interface Props {
  fabricCanvas?: HTMLCanvasElement;
  updateTrigger?: number;
  modelType?: 'box' | 'sphere' | 'plane' | 'cylinder' | 'tshirt';
  textureRepeat?: { x: number; y: number };
  textureOffset?: { x: number; y: number };
}

const Advanced3DTexture: React.FC<Props> = ({ 
  fabricCanvas, 
  updateTrigger, 
  modelType = 'tshirt',
  textureRepeat = { x: 1, y: 1 },
  textureOffset = { x: 0, y: 0 }
}) => {
  const textureRef = useRef<THREE.CanvasTexture | null>(null);
  const { scene } = useGLTF('/assets/tshirt.glb');

  // Initialize texture when canvas appears
  const texture = useMemo(() => {
    if (!fabricCanvas) return null;
    const tex = new THREE.CanvasTexture(fabricCanvas);
    tex.needsUpdate = true;
    tex.flipY = false; // Important for GLB models
    tex.wrapS = THREE.RepeatWrapping;
    tex.wrapT = THREE.RepeatWrapping;
    tex.repeat.set(textureRepeat.x, textureRepeat.y);
    tex.offset.set(textureOffset.x, textureOffset.y);
    textureRef.current = tex;
    return tex;
  }, [fabricCanvas, textureRepeat.x, textureRepeat.y, textureOffset.x, textureOffset.y]);

  // Force update when fabricCanvas changes or updateTrigger changes
  useEffect(() => {
    if (textureRef.current) {
      textureRef.current.needsUpdate = true;
    }
  }, [fabricCanvas, updateTrigger]);

  // Update texture repeat and offset
  useEffect(() => {
    if (textureRef.current) {
      textureRef.current.repeat.set(textureRepeat.x, textureRepeat.y);
      textureRef.current.offset.set(textureOffset.x, textureOffset.y);
      textureRef.current.needsUpdate = true;
    }
  }, [textureRepeat.x, textureRepeat.y, textureOffset.x, textureOffset.y]);

  // Apply texture to the model
  useEffect(() => {
    if (texture && scene && modelType === 'tshirt') {
      scene.traverse((child) => {
        if (child instanceof THREE.Mesh) {
          if (child.material) {
            if (Array.isArray(child.material)) {
              child.material.forEach((mat) => {
                if (mat instanceof THREE.MeshStandardMaterial) {
                  mat.map = texture;
                  mat.needsUpdate = true;
                }
              });
            } else if (child.material instanceof THREE.MeshStandardMaterial) {
              child.material.map = texture;
              child.material.needsUpdate = true;
            }
          }
        }
      });
    }
  }, [texture, scene, modelType]);

  // Render different geometries based on modelType
  const renderGeometry = () => {
    if (modelType === 'tshirt') {
      return (
        <primitive 
          object={scene} 
          scale={[1, 1, 1]} 
          position={[0, 0, 0]}
          rotation={[0, 0, 0]}
        />
      );
    }

    // Fallback to basic geometries
    switch (modelType) {
      case 'sphere':
        return (
          <mesh rotation={[0.5, 0.5, 0]}>
            <sphereGeometry args={[1, 32, 32]} />
            <meshStandardMaterial 
              map={texture ?? undefined} 
              transparent={true}
              alphaTest={0.1}
            />
          </mesh>
        );
      case 'plane':
        return (
          <mesh rotation={[0.5, 0.5, 0]}>
            <planeGeometry args={[2, 2]} />
            <meshStandardMaterial 
              map={texture ?? undefined} 
              transparent={true}
              alphaTest={0.1}
            />
          </mesh>
        );
      case 'cylinder':
        return (
          <mesh rotation={[0.5, 0.5, 0]}>
            <cylinderGeometry args={[1, 1, 2, 32]} />
            <meshStandardMaterial 
              map={texture ?? undefined} 
              transparent={true}
              alphaTest={0.1}
            />
          </mesh>
        );
      case 'box':
      default:
        return (
          <mesh rotation={[0.5, 0.5, 0]}>
            <boxGeometry args={[2, 2, 2]} />
            <meshStandardMaterial 
              map={texture ?? undefined} 
              transparent={true}
              alphaTest={0.1}
            />
          </mesh>
        );
    }
  };

  return <>{renderGeometry()}</>;
};

// Preload the GLB file
useGLTF.preload('/assets/tshirt.glb');

export default Advanced3DTexture;
