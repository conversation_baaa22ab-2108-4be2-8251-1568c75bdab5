import React, { useState, useCallback } from "react";
import { Canvas } from "@react-three/fiber";
import { OrbitControls, Environment } from "@react-three/drei";
import FabricCanvas from "./FabricCanvas";
import Advanced3DTexture from "../components/Advanced3DTexture";
import TextureControls from "../components/TextureControls";

const AdvancedTexturePage: React.FC = () => {
  const [fabricCanvas, setFabricCanvas] = useState<HTMLCanvasElement>();
  const [updateTrigger, setUpdateTrigger] = useState(0);
  const [modelType, setModelType] = useState<'box' | 'sphere' | 'plane' | 'cylinder' | 'tshirt'>('tshirt');
  const [textureRepeat, setTextureRepeat] = useState({ x: 1, y: 1 });
  const [textureOffset, setTextureOffset] = useState({ x: 0, y: 0 });
  const [exportedImages, setExportedImages] = useState<string[]>([]);

  const handleCanvasUpdate = useCallback(() => {
    setUpdateTrigger(prev => prev + 1);
  }, []);

  const handleExport = useCallback((dataURL: string) => {
    setExportedImages(prev => [dataURL, ...prev.slice(0, 4)]); // Keep last 5 exports
    
    // Create download link
    const link = document.createElement('a');
    link.download = `fabric-texture-${Date.now()}.png`;
    link.href = dataURL;
    link.click();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-center mb-6">Advanced Fabric Canvas to 3D Texture</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column: Fabric Canvas */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-4">
              <h2 className="text-xl font-semibold mb-4">Design Canvas</h2>
              <FabricCanvas 
                onReady={setFabricCanvas} 
                onUpdate={handleCanvasUpdate}
                onExport={handleExport}
              />
            </div>
          </div>

          {/* Middle Column: 3D Preview */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg shadow-lg p-4 h-full">
              <h2 className="text-xl font-semibold mb-4">3D Preview</h2>
              <div className="h-96 bg-gray-900 rounded">
                <Canvas camera={{ position: [0, 1.5, 3], fov: 50 }}>
                  <ambientLight intensity={0.5} />
                  <directionalLight position={[2, 2, 2]} intensity={1} />
                  <OrbitControls enablePan={true} enableZoom={true} enableRotate={true} />
                  <Environment preset="studio" />
                  
                  {fabricCanvas && (
                    <Advanced3DTexture 
                      fabricCanvas={fabricCanvas} 
                      updateTrigger={updateTrigger}
                      modelType={modelType}
                      textureRepeat={textureRepeat}
                      textureOffset={textureOffset}
                    />
                  )}
                </Canvas>
              </div>
            </div>
          </div>

          {/* Right Column: Controls and Export History */}
          <div className="lg:col-span-1 space-y-6">
            {/* Texture Controls */}
            <div className="bg-white rounded-lg shadow-lg p-4">
              <TextureControls
                modelType={modelType}
                onModelTypeChange={setModelType}
                textureRepeat={textureRepeat}
                onTextureRepeatChange={setTextureRepeat}
                textureOffset={textureOffset}
                onTextureOffsetChange={setTextureOffset}
              />
            </div>

            {/* Export History */}
            {exportedImages.length > 0 && (
              <div className="bg-white rounded-lg shadow-lg p-4">
                <h3 className="text-lg font-semibold mb-4">Recent Exports</h3>
                <div className="grid grid-cols-2 gap-2">
                  {exportedImages.map((imageUrl, index) => (
                    <div key={index} className="relative">
                      <img 
                        src={imageUrl} 
                        alt={`Export ${index + 1}`}
                        className="w-full h-20 object-cover rounded border"
                      />
                      <div className="absolute top-1 right-1 bg-black bg-opacity-50 text-white text-xs px-1 rounded">
                        {index + 1}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Instructions */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h3 className="text-lg font-semibold mb-2 text-blue-800">How to Use:</h3>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Draw on the canvas using the toolbar</li>
                <li>• See real-time updates on the 3D model</li>
                <li>• Change model type and texture settings</li>
                <li>• Use mouse to rotate, zoom, and pan the 3D view</li>
                <li>• Click "Export" to download your texture</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedTexturePage;
