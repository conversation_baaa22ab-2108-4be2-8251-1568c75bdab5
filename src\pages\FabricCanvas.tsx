import React, { useEffect, useRef, useState } from "react";
import * as fabric  from "fabric"; // 👈 correct import for v6
import {
  addText,
  addRectangle,
  addCircle,
  addLine,
} from "../components/CanvasTools";
import CanvasSettings from "../components/CanvasSettings";
import { Type, Square, Circle as CircleIcon, Minus } from "lucide-react";

const CANVAS_WIDTH = 800;
const CANVAS_HEIGHT = 500;

interface FabricCanvasProps {
  onReady?: (canvasEl: HTMLCanvasElement) => void;
}

const FabricCanvas: React.FC<FabricCanvasProps> = ({ onReady }) => {
  const canvasElRef = useRef<HTMLCanvasElement | null>(null);
  const fabricRef = useRef<fabric.Canvas | null>(null);
  const [canvas, setCanvas] = useState<fabric.Canvas | null>(null);

  useEffect(() => {
    if (!canvasElRef.current) return;

    const c = new fabric.Canvas(canvasElRef.current, {
      height: CANVAS_HEIGHT,
      width: CANVAS_WIDTH,
      backgroundColor: "#f5f5f5",
    });

    fabricRef.current = c;
    setCanvas(c);
    c.renderAll();

    // ✅ send the lowerCanvasEl (the actual drawing surface)
    if (onReady && c.lowerCanvasEl) {
      onReady(c.lowerCanvasEl);
    }

    return () => {
      c.dispose();
      fabricRef.current = null;
      setCanvas(null);
    };
  }, [onReady]);

  return (
    <div className="p-4">
      {/* Toolbar */}
      <div className="flex gap-2 mb-4">
        <button
          type="button"
          aria-label="Add text"
          title="Add text"
          className="p-2 bg-gray-200 rounded hover:bg-gray-300 flex items-center justify-center"
          onClick={() => canvas && addText(canvas)}
        >
          <Type size={20} aria-hidden />
        </button>
        <button
          type="button"
          aria-label="Add rectangle"
          title="Add rectangle"
          className="p-2 bg-gray-200 rounded hover:bg-gray-300 flex items-center justify-center"
          onClick={() => canvas && addRectangle(canvas)}
        >
          <Square size={20} aria-hidden />
        </button>
        <button
          type="button"
          aria-label="Add circle"
          title="Add circle"
          className="p-2 bg-gray-200 rounded hover:bg-gray-300 flex items-center justify-center"
          onClick={() => canvas && addCircle(canvas)}
        >
          <CircleIcon size={20} aria-hidden />
        </button>
        <button
          type="button"
          aria-label="Add line"
          title="Add line"
          className="p-2 bg-gray-200 rounded hover:bg-gray-300 flex items-center justify-center"
          onClick={() => canvas && addLine(canvas)}
        >
          <Minus size={20} aria-hidden />
        </button>
      </div>

      {/* Canvas + Settings */}
      <div className="flex gap-6 items-start">
        <canvas
          ref={canvasElRef}
          width={CANVAS_WIDTH}
          height={CANVAS_HEIGHT}
          role="img"
          aria-label="Design canvas"
          className="border border-gray-300 w-[800px] h-[500px] rounded"
        />
        <CanvasSettings canvas={canvas} />
      </div>
    </div>
  );
};

export default FabricCanvas;
