import { useEffect, useState } from "react";
import type { Canvas, Object as FabricObject, Rect, Circle } from "fabric";

interface CanvasSettingsProps {
  canvas: Canvas | null;
}

export default function CanvasSettings({ canvas }: CanvasSettingsProps) {
  const [selectedObject, setSelectedObject] = useState<FabricObject | null>(null);
  const [width, setWidth] = useState<string>("");
  const [height, setHeight] = useState<string>("");
  const [diameter, setDiameter] = useState<string>("");
  const [color, setColor] = useState<string>("");

  useEffect(() => {
    if (!canvas) return;

    const handleSelection = (e: any) => {
      const obj = e.selected?.[0] ?? canvas.getActiveObject();
      applySelection(obj as FabricObject | null);
    };

    const handleCleared = () => applySelection(null);

    const handleObjectModified = (e: any) => applySelection(e.target as FabricObject);
    const handleObjectScaling = (e: any) => applySelection(e.target as FabricObject);

    canvas.on("selection:created", handleSelection);
    canvas.on("selection:updated", handleSelection);
    canvas.on("selection:cleared", handleCleared);
    canvas.on("object:modified", handleObjectModified);
    canvas.on("object:scaling", handleObjectScaling);

    if (canvas.getActiveObject()) applySelection(canvas.getActiveObject() as FabricObject);

    return () => {
      canvas.off("selection:created", handleSelection);
      canvas.off("selection:updated", handleSelection);
      canvas.off("selection:cleared", handleCleared);
      canvas.off("object:modified", handleObjectModified);
      canvas.off("object:scaling", handleObjectScaling);
    };
  }, [canvas]);

  function applySelection(obj: FabricObject | null) {
    if (!obj) {
      setSelectedObject(null);
      setWidth("");
      setHeight("");
      setDiameter("");
      setColor("");
      return;
    }

    setSelectedObject(obj);

    if (obj.type === "rect" || obj.type === "rectangle") {
      setWidth(Math.round((obj as Rect).width ?? 0).toString());
      setHeight(Math.round((obj as Rect).height ?? 0).toString());
      setDiameter("");
      setColor((obj.fill as string) ?? "");
    } else if (obj.type === "circle") {
      const dia = Math.round(((obj as Circle).radius ?? 0) * 2);
      setDiameter(dia.toString());
      setWidth("");
      setHeight("");
      setColor((obj.fill as string) ?? "");
    } else {
      setWidth("");
      setHeight("");
      setDiameter("");
      setColor((obj.fill as string) ?? "");
    }
  }

  const updateRectWidth = (val: string) => {
    setWidth(val);
    if (!selectedObject || selectedObject.type !== "rect") return;
    const newW = parseFloat(val);
    if (!isFinite(newW) || newW <= 0) return;
    (selectedObject as Rect).set({ width: newW, scaleX: 1 });
    selectedObject.setCoords();
    canvas?.requestRenderAll();
  };

  const updateRectHeight = (val: string) => {
    setHeight(val);
    if (!selectedObject || selectedObject.type !== "rect") return;
    const newH = parseFloat(val);
    if (!isFinite(newH) || newH <= 0) return;
    (selectedObject as Rect).set({ height: newH, scaleY: 1 });
    selectedObject.setCoords();
    canvas?.requestRenderAll();
  };

  const updateCircleDiameter = (val: string) => {
    setDiameter(val);
    if (!selectedObject || selectedObject.type !== "circle") return;
    const newD = parseFloat(val);
    if (!isFinite(newD) || newD <= 0) return;
    (selectedObject as Circle).set({ radius: newD / 2, scaleX: 1, scaleY: 1 });
    selectedObject.setCoords();
    canvas?.requestRenderAll();
  };

  const updateColor = (val: string) => {
    setColor(val);
    if (!selectedObject) return;
    selectedObject.set("fill", val);
    canvas?.requestRenderAll();
  };

  const deleteSelected = () => {
    if (!selectedObject || !canvas) return;
    canvas.remove(selectedObject);
    canvas.discardActiveObject();
    canvas.requestRenderAll();
    applySelection(null);
  };

  return (
    <div className="p-4 bg-white rounded shadow w-64">
      <h3 className="text-lg font-semibold mb-3">Settings</h3>

      {!selectedObject ? (
        <p className="text-sm text-gray-500">No object selected</p>
      ) : (
        <div className="space-y-3">
          {(selectedObject.type === "rect" || selectedObject.type === "rectangle") && (
            <>
              <label className="block text-xs" htmlFor="rect-width">
                Width (px)
              </label>
              <input
                id="rect-width"
                className="w-full p-1 border rounded"
                value={width}
                onChange={(e) => updateRectWidth(e.target.value)}
                inputMode="numeric"
              />

              <label className="block text-xs" htmlFor="rect-height">
                Height (px)
              </label>
              <input
                id="rect-height"
                className="w-full p-1 border rounded"
                value={height}
                onChange={(e) => updateRectHeight(e.target.value)}
                inputMode="numeric"
              />
            </>
          )}

          {selectedObject.type === "circle" && (
            <>
              <label className="block text-xs" htmlFor="circle-diameter">
                Diameter (px)
              </label>
              <input
                id="circle-diameter"
                className="w-full p-1 border rounded"
                value={diameter}
                onChange={(e) => updateCircleDiameter(e.target.value)}
                inputMode="numeric"
              />
            </>
          )}

          <label className="block text-xs" htmlFor="fill-color-text">
            Fill color
          </label>
          <div className="flex items-center gap-2">
            <input
              id="fill-color"
              type="color"
              title="fill color"
              className="w-10 h-8 p-0 border rounded"
              value={color || "#000000"}
              onChange={(e) => updateColor(e.target.value)}
            />
            <input
              id="fill-color-text"
              className="flex-1 p-1 border rounded"
              value={color}
              onChange={(e) => updateColor(e.target.value)}
            />
          </div>

          <div className="flex gap-2">
            <button
              className="px-3 py-1 bg-red-100 text-red-700 rounded"
              onClick={deleteSelected}
            >
              Delete
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
