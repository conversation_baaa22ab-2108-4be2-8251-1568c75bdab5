import React, { useState, useCallback } from "react";
import FabricCanvas from "./pages/FabricCanvas";
import { Canvas as ThreeCanvas } from "@react-three/fiber";
import { PresentationControls, Stage } from "@react-three/drei";
import FabricTexturedShirt from "./components/FabricTexture";

const App: React.FC = () => {
  const [fabricCanvas, setFabricCanvas] = useState<HTMLCanvasElement | undefined>(
    undefined
  );
  const [updateKey, setUpdateKey] = useState(0);

  const handleCanvasReady = useCallback((canvasEl: HTMLCanvasElement) => {
    setFabricCanvas(canvasEl);
  }, []);

  const handleCanvasModified = useCallback(() => {
    setUpdateKey((prev) => prev + 1);
  }, []);

  return (
    <div className="flex flex-col md:flex-row gap-4 h-screen">
      <div className="flex-1 border p-2">
        <FabricCanvas
          onReady={handleCanvasReady}
          onModified={handleCanvasModified}
        />
      </div>

      <div className="flex-1 border p-2">
        <ThreeCanvas
          camera={{ position: [0, 1, 2.5], fov: 45 }}
          className="w-full h-full"
        >
          <color attach="background" args={["#F8F9FA"]} />
          <PresentationControls>
            <Stage>
              <FabricTexturedShirt
                fabricCanvas={fabricCanvas}
                key={updateKey} // Force re-render
              />
            </Stage>
          </PresentationControls>
        </ThreeCanvas>
      </div>
    </div>
  );
};

export default App;
