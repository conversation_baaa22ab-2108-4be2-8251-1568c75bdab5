// src/App.tsx
import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Link,
} from "react-router-dom";
import FabricCanvas from "./pages/FabricCanvas";
import { Canvas as ThreeCanvas } from "@react-three/fiber";
import { PresentationControls, Stage } from "@react-three/drei";
import FabricTexturedShirt from "./components/FabricTexture";

const Home: React.FC = () => {
  return <h1 className="text-2xl font-bold">Home Page</h1>;
};

const About: React.FC = () => {
  return <h1 className="text-2xl font-bold">About Page</h1>;
};

// This is the canvas page with Fabric.js + 3D model
const CanvasPage: React.FC = () => {
  // Get fabricCanvas element from FabricCanvas
  const [fabricCanvas, setFabricCanvas] = React.useState<
    HTMLCanvasElement | undefined
  >(undefined);

  // Counter to trigger texture updates
  const [updateTrigger, setUpdateTrigger] = React.useState(0);

  // Function to trigger texture update
  const handleCanvasUpdate = React.useCallback(() => {
    setUpdateTrigger(prev => prev + 1);
  }, []);

  return (
    <div className="flex flex-col md:flex-row gap-4 h-[80vh]">
      {/* Left side: Fabric.js drawing canvas */}
      <div className="flex-1 border p-2">
        <FabricCanvas onReady={setFabricCanvas} onUpdate={handleCanvasUpdate} />
      </div>

      {/* Right side: 3D shirt preview */}
      <div className="flex-1 border p-2">
        <ThreeCanvas camera={{ position: [0, 1, 2.5], fov: 45 }} className="w-full h-full">
          <color attach="background" args={["#F8F9FA"]} />
          <PresentationControls>
            <Stage>
              <FabricTexturedShirt fabricCanvas={fabricCanvas} updateTrigger={updateTrigger} />
            </Stage>
          </PresentationControls>
        </ThreeCanvas>
      </div>
    </div>
  );
};

const App: React.FC = () => {
  return (
    <Router>
      <div className="p-6">
        <nav className="flex gap-4 mb-6">
          <Link to="/" className="text-blue-500 hover:underline">
            Home
          </Link>
          <Link to="/canvas" className="text-blue-500 hover:underline">
            Canvas
          </Link>
          <Link to="/about" className="text-blue-500 hover:underline">
            About
          </Link>
        </nav>

        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/canvas" element={<CanvasPage />} />
          <Route path="/about" element={<About />} />
        </Routes>
      </div>
    </Router>
  );
};

export default App;
